# Changelog

## 2.0.0 (2025-09-22)

## Breaking changes

- Strongly typed the majority of the API to return `FileInfo` and `DirectoryInfo` instead of string and string arrays.

## Added

- Unity UI custom components
- Tests

## 1.2.0 (2022-05-23)

### Changed

- Add support for Unity 2021.3f1 LTS
- Update Windows wrapper library from `System.Windows.Forms.dll` to
  `ShellFileDialog.dll` ([#122](https://github.com/gkngkc/UnityStandaloneFileBrowser/issues/122#issuecomment-1121909572))

## [1.1.3](https://github.com/adrenak/usfb/compare/v1.1.2...v1.1.3) (2020-04-08)

### Bug Fixes

* Delete package-lock.json ([12a290d](https://github.com/adrenak/usfb/commit/12a290d8639f93e2c0848360f714cd485fca0529))

## [1.1.2](https://github.com/adrenak/usfb/compare/v1.1.1...v1.1.2) (2020-04-08)

### Bug Fixes

* Get Runtime and Runtime Common asmdefs to run on any
  supported ([3f0cd4b](https://github.com/adrenak/usfb/commit/3f0cd4bc23033aa3a3f8cc4844643adaa213d02c))

## [1.1.1](https://github.com/adrenak/usfb/compare/v1.1.0...v1.1.1) (2020-04-08)

### Bug Fixes

* Reorganise source for platform dependent compilation.
  Experimental... ([6c0df62](https://github.com/adrenak/usfb/commit/6c0df62a73985a532d4dabefeaa9e94c671ab0d7))

## [1.1.0](https://github.com/adrenak/usfb/compare/v1.0.0...v1.1.0) (2020-04-08)

### Features

* Change name from sfb to
  usfb ([04669e1](https://github.com/adrenak/usfb/commit/04669e1663ea86e1103ecb07ebbafc3ec7ea5445))

## 1.0.0 (2020-04-08)

### Features

* Modify for
  UPM ([4964bd9](https://github.com/adrenak/UnityStandaloneFileBrowser/commit/4964bd9ce93fe6b8938a2d30d7bec5da9d896a32))
