0000000000000000000000000000000000000000 f7e2074cc5c5379d2a1a9c5f1fba868ac4697fc7 Sov3rain <<EMAIL>> 1758097431 +0200	clone: from https://github.com/Sov3rain/unity-standalone-file-browser.git
f7e2074cc5c5379d2a1a9c5f1fba868ac4697fc7 3dd75ba380ccc2897741b63be76eaa447fe3c0dd Sov3rain <<EMAIL>> 1758097548 +0200	commit: chore: update to unity 2021.3.45f1
3dd75ba380ccc2897741b63be76eaa447fe3c0dd e6af7726ebbeb628d32f57e1e747a77b150959dc Sov3rain <<EMAIL>> 1758097746 +0200	commit: chore: update rider package
e6af7726ebbeb628d32f57e1e747a77b150959dc 677efa742ef4e324cece903f1863cbee347126d0 Sov3rain <<EMAIL>> 1758099089 +0200	commit: chore: add TextMeshPro package, move Editor folder out of runtime folder, format and cleanup code and comments
677efa742ef4e324cece903f1863cbee347126d0 db781ce6524fcea2f9d3ced3f337bdd4910ac0cd Sov3rain <<EMAIL>> 1758110391 +0200	commit: chore: refactor file and folder panel methods to return `FileSystemInfo` objects, update related demos and improve code readability
db781ce6524fcea2f9d3ced3f337bdd4910ac0cd 32d8e625a9b7d1b4dae5c6259f9becd3e946b3d0 Sov3rain <<EMAIL>> 1758112330 +0200	commit: chore: enhance `StandaloneFileBrowser` API with improved parameter validation, updated XML documentation, and consistent null-handling
32d8e625a9b7d1b4dae5c6259f9becd3e946b3d0 4f67dccbddded919031832624e2a81e55abbf821 Sov3rain <<EMAIL>> 1758112575 +0200	commit: chore: remove redundant null checks for callbacks in `StandaloneFileBrowser` methods, update XML documentation to reflect optional nature
4f67dccbddded919031832624e2a81e55abbf821 5314190432f740a97dfddb74d077ff3c2b9ef20c Sov3rain <<EMAIL>> 1758114945 +0200	commit: chore: add optional callback null-checks to `StandaloneFileBrowser` methods, update demos with examples for no-callback usage
5314190432f740a97dfddb74d077ff3c2b9ef20c a6c50a20389d4dd164454a84db435113a22beecc Sov3rain <<EMAIL>> ********** +0200	commit: chore: cleanup `.idea` project files and update `.gitignore` with JetBrains Rider folder exclusion
a6c50a20389d4dd164454a84db435113a22beecc 3cf575cc9f16ea278cbd261bd0bedc177a5695c2 Sov3rain <<EMAIL>> ********** +0200	merge tests: Merge made by the 'ort' strategy.
3cf575cc9f16ea278cbd261bd0bedc177a5695c2 028e9fd743e6b549c0a8caf922940f76a8e354b5 Sov3rain <<EMAIL>> ********** +0200	commit: chore: remove unused CI configuration, LICENSE, and legacy meta files, update README and package details for restructured project organization
028e9fd743e6b549c0a8caf922940f76a8e354b5 ea87f51d4fce47d95391dbd20869361ee1a5b5ef Sov3rain <<EMAIL>> ********** +0200	commit: chore: update `OpenFileInput` implementation to support stronger typing, add Unity Events for file selection, refactor UI handling, and improve prefab creation logic
ea87f51d4fce47d95391dbd20869361ee1a5b5ef e60e34a71e447d95c5386d13255d67915ab97926 Sov3rain <<EMAIL>> ********** +0200	commit: feat: add `OpenFolderInput` component with Unity Events, prefab creation, and updated UI elements for folder selection functionality
e60e34a71e447d95c5386d13255d67915ab97926 42e5a7b12ad8c9477cb3c44ccc43cdc80732d0c5 Sov3rain <<EMAIL>> ********** +0200	commit: feat: add `SaveFileInput` component with Unity Events, associated prefab, and enhanced UI elements for save file functionality
42e5a7b12ad8c9477cb3c44ccc43cdc80732d0c5 7291f2075ac57a013c8cf4c9056f407354e9b807 Sov3rain <<EMAIL>> 1758615467 +0200	commit: chore: refactor `FileInput` components to use base class approach, remove redundant UI references, and extend `CreateFromHierarchyMenu` with `SaveFileInput` creation option
7291f2075ac57a013c8cf4c9056f407354e9b807 21fe07ab619f3fff27a4aa37548d3ed13be61a7f Sov3rain <<EMAIL>> 1758615553 +0200	commit: chore: remove obsolete test suite and associated metadata files for `Unity Standalone File Browser`, cleanup no longer needed resources
21fe07ab619f3fff27a4aa37548d3ed13be61a7f 342e5b81c34107450368e16dcfdbd2dfc41b87e3 Sov3rain <<EMAIL>> 1758619891 +0200	commit: docs: update README with new UI components, example usage, and enhanced API documentation, add related component screenshots
342e5b81c34107450368e16dcfdbd2dfc41b87e3 dfc19cce0042c41e27c84d305d804daaac38deb1 Sov3rain <<EMAIL>> 1758619944 +0200	commit: docs: update README to clarify package installation instructions with additional git URL option
