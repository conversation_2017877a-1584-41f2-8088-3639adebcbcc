[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = https://github.com/Sov3rain/unity-standalone-file-browser.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "master"]
	remote = origin
	merge = refs/heads/master
	vscode-merge-base = origin/master
[remote "upstream"]
	url = https://github.com/adrenak/usfb.git
	fetch = +refs/heads/*:refs/remotes/upstream/*
	gh-resolved = base
