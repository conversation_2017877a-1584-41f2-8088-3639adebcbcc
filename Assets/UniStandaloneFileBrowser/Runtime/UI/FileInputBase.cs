using TMPro;
using UnityEngine;
using UnityEngine.UI;

[DisallowMultipleComponent]
public class FileInputBase: MonoBehaviour
{
    [<PERSON><PERSON>("UI Elements")]
    [SerializeField] protected Button _button;
    [SerializeField] protected TMP_Text _text;
    
    [<PERSON><PERSON>("File Input Settings")]
    [Serial<PERSON><PERSON><PERSON>, Too<PERSON><PERSON>("Dialog title")]
    protected string _title;

    [SerializeField, Toolt<PERSON>("Root directory")]
    protected string _directory;
}