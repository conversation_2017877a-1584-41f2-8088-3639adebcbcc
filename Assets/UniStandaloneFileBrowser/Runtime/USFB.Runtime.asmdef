{"name": "USFB.Runtime", "rootNamespace": "", "references": ["USFB.Runtime.Common", "USFB.Runtime.Linux", "USFB.Runtime.Mac", "USFB.Runtime.Windows", "Unity.TextMeshPro", "USFB.Runtime.WebGL"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}